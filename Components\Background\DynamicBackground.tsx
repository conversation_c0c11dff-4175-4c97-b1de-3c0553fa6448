import React, { useEffect, useRef, useState } from 'react';
import { gsap } from 'gsap';
import * as SunCalc from 'suncalc';
import AstronomicalLayer from './AstronomicalLayer';
import DiurnalLayer from './DiurnalLayer';
import { useTime } from '../Context/TimeContext';
import { useLocation } from '../Context/LocationContext';

// Types pour les couleurs du cycle solaire naturel
interface SolarPhaseColor {
  phase: string;
  colors: {
    primary: string;
    secondary: string;
    tertiary: string;
  };
}

// Énumération des phases solaires pour une progression naturelle
enum SolarPhase {
  DEEP_NIGHT = 'deep_night',
  ASTRONOMICAL_DAWN = 'astronomical_dawn',
  NAUTICAL_DAWN = 'nautical_dawn',
  CIVIL_DAWN = 'civil_dawn',
  SUNRISE = 'sunrise',
  MORNING = 'morning',
  MIDDAY = 'midday',
  AFTERNOON = 'afternoon',
  CIVIL_DUSK = 'civil_dusk',
  SUNSET = 'sunset',
  NAUTICAL_DUSK = 'nautical_dusk',
  ASTRONOMICAL_DUSK = 'astronomical_dusk'
}

// Palette de couleurs naturelles basée sur les phases solaires réelles
// Format: { primary: bas (horizon), secondary: milieu, tertiary: haut (ciel) }
// Couleurs inspirées de la nature, douces et réalistes
const SOLAR_PHASE_COLORS: SolarPhaseColor[] = [
  // Nuit profonde - Bleu-gris naturel très sombre 🌌
  {
    phase: SolarPhase.DEEP_NIGHT,
    colors: {
      primary: '#2c3e50',   // Bleu-gris sombre naturel
      secondary: '#34495e', // Gris-bleu moyen
      tertiary: '#1a252f'   // Bleu très sombre
    }
  },

  // Aube astronomique - Premières lueurs très subtiles 🌠
  {
    phase: SolarPhase.ASTRONOMICAL_DAWN,
    colors: {
      primary: '#3a4a5c',   // Bleu-gris légèrement plus clair
      secondary: '#4a5568', // Gris-bleu
      tertiary: '#2c3e50'   // Bleu-gris sombre
    }
  },

  // Aube nautique - Horizon commence à se distinguer ⚓
  {
    phase: SolarPhase.NAUTICAL_DAWN,
    colors: {
      primary: '#4a5d70',   // Bleu-gris plus clair
      secondary: '#5a6b7d', // Gris-bleu clair
      tertiary: '#3a4a5c'   // Bleu-gris moyen
    }
  },

  // Aube civile - Premières couleurs chaudes très douces 🌅
  {
    phase: SolarPhase.CIVIL_DAWN,
    colors: {
      primary: '#6b7d8a',   // Gris-bleu doux
      secondary: '#7a8c99', // Gris-bleu clair
      tertiary: '#4a5d70'   // Bleu-gris
    }
  },

  // 🌅 LEVER DU SOLEIL - Palette photographique réaliste (bleu s'éclaircit en haut)
  {
    phase: SolarPhase.SUNRISE,
    colors: {
      primary: '#ce6a6b',   // Rouge corail (horizon)
      secondary: '#ebaca2', // Rose saumon (milieu)
      tertiary: '#212e53'   // Bleu marine foncé (ciel haut)
    }
  },

  // 🌄 MATIN - Continuation des couleurs douces de l'aube
  {
    phase: SolarPhase.MORNING,
    colors: {
      primary: '#bed3c3',   // Vert doux (horizon)
      secondary: '#4a919e', // Bleu-vert (milieu)
      tertiary: '#212e53'   // Bleu marine foncé (ciel haut)
    }
  },

  // ☀️ MIDI - Bleu d'été intense (bleu clair horizon → bleu intense en haut)
  {
    phase: SolarPhase.MIDDAY,
    colors: {
      primary: '#87ceeb',   // Bleu ciel clair (horizon)
      secondary: '#4682b4', // Bleu acier (milieu)
      tertiary: '#1e90ff'   // Bleu intense (ciel haut)
    }
  },

  // 🌤️ APRÈS-MIDI - Bleu d'été avec nuances chaudes
  {
    phase: SolarPhase.AFTERNOON,
    colors: {
      primary: '#87ceeb',   // Bleu ciel clair (horizon)
      secondary: '#4682b4', // Bleu acier (milieu)
      tertiary: '#1e90ff'   // Bleu intense (ciel haut)
    }
  },

  // 🌆 CRÉPUSCULE CIVIL - Transition avec violets
  {
    phase: SolarPhase.CIVIL_DUSK,
    colors: {
      primary: '#b93d23',   // Rouge brique (horizon)
      secondary: '#654b62', // Violet moyen (milieu)
      tertiary: '#553753'   // Violet foncé (ciel haut)
    }
  },

  // 🌇 COUCHER DE SOLEIL - Palette photographique réaliste (bleu sombre en haut)
  {
    phase: SolarPhase.SUNSET,
    colors: {
      primary: '#ffb937',   // Jaune doré (horizon)
      secondary: '#f17133', // Orange vif (milieu)
      tertiary: '#553753'   // Violet foncé (ciel haut)
    }
  },

  // Crépuscule nautique - Transition vers les bleus sombres ⚓
  {
    phase: SolarPhase.NAUTICAL_DUSK,
    colors: {
      primary: '#8a9ba8',   // Gris-bleu
      secondary: '#7a8c99', // Gris-bleu plus sombre
      tertiary: '#6b7d8a'   // Gris-bleu sombre
    }
  },

  // Crépuscule astronomique - Retour vers la nuit 🌠
  {
    phase: SolarPhase.ASTRONOMICAL_DUSK,
    colors: {
      primary: '#4a5d70',   // Bleu-gris sombre
      secondary: '#3a4a5c', // Bleu-gris plus sombre
      tertiary: '#2c3e50'   // Bleu-gris très sombre
    }
  }
];

// Interface pour les props du composant
interface DynamicBackgroundProps {
  children: React.ReactNode;
}

const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ children }) => {
  const { getCurrentTime } = useTime();
  const { userLocation, locationReady } = useLocation();
  const backgroundRef = useRef<HTMLDivElement>(null);
  const landscapeRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<gsap.core.Timeline | null>(null);
  const zoomTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const lastUpdateRef = useRef<string>('');
  const animationFrameRef = useRef<number | null>(null);
  const [landscapeBrightness, setLandscapeBrightness] = useState(1);

  // Fonction pour déterminer la phase solaire actuelle basée sur SunCalc
  const getCurrentSolarPhase = (currentTime: Date): SolarPhase => {
    if (!locationReady) return SolarPhase.DEEP_NIGHT;

    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    // Convertir les heures SunCalc en format décimal
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const civilDawn = sunTimes.dawn.getHours() + sunTimes.dawn.getMinutes() / 60;
    const civilDusk = sunTimes.dusk.getHours() + sunTimes.dusk.getMinutes() / 60;
    const nauticalDawn = sunTimes.nauticalDawn.getHours() + sunTimes.nauticalDawn.getMinutes() / 60;
    const nauticalDusk = sunTimes.nauticalDusk.getHours() + sunTimes.nauticalDusk.getMinutes() / 60;
    const astronomicalDawn = sunTimes.nightEnd.getHours() + sunTimes.nightEnd.getMinutes() / 60;
    const astronomicalDusk = sunTimes.night.getHours() + sunTimes.night.getMinutes() / 60;

    // 🔧 CORRECTION: Transitions plus naturelles et visuellement cohérentes

    // Phases du matin (de l'aube au lever du soleil)
    if (currentHour >= astronomicalDawn && currentHour < nauticalDawn) return SolarPhase.ASTRONOMICAL_DAWN;
    if (currentHour >= nauticalDawn && currentHour < civilDawn) return SolarPhase.NAUTICAL_DAWN;
    if (currentHour >= civilDawn && currentHour < sunrise - 0.5) return SolarPhase.CIVIL_DAWN;

    // 🔧 CORRECTION: LEVER DE SOLEIL photographique : 30min avant → 30min après
    if (currentHour >= sunrise - 0.5 && currentHour <= sunrise + 0.5) return SolarPhase.SUNRISE;

    // Plein jour - transitions plus douces
    if (currentHour >= sunrise + 1 && currentHour < sunrise + 3) return SolarPhase.MORNING;
    if (currentHour >= sunrise + 3 && currentHour <= 14) return SolarPhase.MIDDAY;

    // Après-midi étendu jusqu'à 1h30 avant le coucher de soleil
    if (currentHour > 14 && currentHour <= sunset - 1.5) return SolarPhase.AFTERNOON;

    // 🔧 CORRECTION: Transitions photographiques réalistes (expertise Cisco)
    // COUCHER DE SOLEIL : 30min avant → 30min après (fenêtre de 1h)
    if (currentHour > sunset - 0.5 && currentHour <= sunset + 0.5) return SolarPhase.SUNSET;

    // CRÉPUSCULE CIVIL : commence 30min après le coucher de soleil
    if (currentHour > sunset + 0.5 && currentHour <= civilDusk) return SolarPhase.CIVIL_DUSK;
    if (currentHour > civilDusk && currentHour <= nauticalDusk) return SolarPhase.NAUTICAL_DUSK;
    if (currentHour > nauticalDusk && currentHour <= astronomicalDusk) return SolarPhase.ASTRONOMICAL_DUSK;

    // Nuit profonde
    return SolarPhase.DEEP_NIGHT;
  };

  // Fonction pour obtenir les couleurs d'une phase solaire
  const getColorsForPhase = (phase: SolarPhase): { primary: string; secondary: string; tertiary: string } => {
    const phaseColor = SOLAR_PHASE_COLORS.find(pc => pc.phase === phase);
    return phaseColor ? phaseColor.colors : SOLAR_PHASE_COLORS[0].colors;
  };

  // Fonction pour interpoler entre deux couleurs avec courbe d'easing douce
  const interpolateColor = (color1: string, color2: string, factor: number): string => {
    // Appliquer une courbe d'easing pour des transitions plus naturelles
    const easedFactor = factor < 0.5
      ? 2 * factor * factor
      : 1 - Math.pow(-2 * factor + 2, 2) / 2;

    const hex1 = color1.replace('#', '');
    const hex2 = color2.replace('#', '');

    const r1 = parseInt(hex1.substring(0, 2), 16);
    const g1 = parseInt(hex1.substring(2, 4), 16);
    const b1 = parseInt(hex1.substring(4, 6), 16);

    const r2 = parseInt(hex2.substring(0, 2), 16);
    const g2 = parseInt(hex2.substring(2, 4), 16);
    const b2 = parseInt(hex2.substring(4, 6), 16);

    const r = Math.round(r1 + (r2 - r1) * easedFactor);
    const g = Math.round(g1 + (g2 - g1) * easedFactor);
    const b = Math.round(b1 + (b2 - b1) * easedFactor);

    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  };

  // Fonction pour calculer la luminosité du paysage basée sur la position solaire réelle
  const calculateLandscapeBrightness = (currentTime: Date): number => {
    if (!locationReady) return 0.2; // Nuit par défaut si géolocalisation pas prête

    const sunTimes = SunCalc.getTimes(currentTime, userLocation.lat, userLocation.lon);
    const currentHour = currentTime.getHours() + currentTime.getMinutes() / 60 + currentTime.getSeconds() / 3600;

    // Convertir les heures SunCalc en format décimal
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const civilDawn = sunTimes.dawn.getHours() + sunTimes.dawn.getMinutes() / 60;
    const civilDusk = sunTimes.dusk.getHours() + sunTimes.dusk.getMinutes() / 60;
    const nauticalDawn = sunTimes.nauticalDawn.getHours() + sunTimes.nauticalDawn.getMinutes() / 60;
    const nauticalDusk = sunTimes.nauticalDusk.getHours() + sunTimes.nauticalDusk.getMinutes() / 60;

    // Plein jour - luminosité maximale
    if (currentHour >= sunrise + 1 && currentHour <= sunset - 1) {
      return 1.0; // Pleine luminosité
    }

    // Lever du soleil - transition progressive vers la pleine luminosité
    if (currentHour >= sunrise && currentHour < sunrise + 1) {
      const progress = (currentHour - sunrise);
      return 0.6 + (0.4 * progress); // De 60% à 100%
    }

    // Aube civile - augmentation de la luminosité
    if (currentHour >= civilDawn && currentHour < sunrise) {
      const progress = (currentHour - civilDawn) / (sunrise - civilDawn);
      return 0.3 + (0.3 * progress); // De 30% à 60%
    }

    // Aube nautique - premières lueurs
    if (currentHour >= nauticalDawn && currentHour < civilDawn) {
      const progress = (currentHour - nauticalDawn) / (civilDawn - nauticalDawn);
      return 0.2 + (0.1 * progress); // De 20% à 30%
    }

    // Coucher du soleil - transition progressive vers l'obscurité
    if (currentHour > sunset - 1 && currentHour <= sunset) {
      const progress = (currentHour - (sunset - 1));
      return 1.0 - (0.4 * progress); // De 100% à 60%
    }

    // Crépuscule civil - diminution de la luminosité
    if (currentHour > sunset && currentHour <= civilDusk) {
      const progress = (currentHour - sunset) / (civilDusk - sunset);
      return 0.6 - (0.3 * progress); // De 60% à 30%
    }

    // Crépuscule nautique - dernières lueurs
    if (currentHour > civilDusk && currentHour <= nauticalDusk) {
      const progress = (currentHour - civilDusk) / (nauticalDusk - civilDusk);
      return 0.3 - (0.1 * progress); // De 30% à 20%
    }

    // Nuit profonde
    return 0.2; // 20% de luminosité pour la nuit
  };

  // Fonction pour obtenir les couleurs actuelles basées sur la position solaire réelle
  const getCurrentColors = () => {
    const now = getCurrentTime(); // Utiliser le temps du contexte (réel ou simulé)

    if (!locationReady) {
      // Si la géolocalisation n'est pas prête, utiliser les couleurs de nuit profonde
      return getColorsForPhase(SolarPhase.DEEP_NIGHT);
    }

    // Obtenir la phase solaire actuelle
    const currentPhase = getCurrentSolarPhase(now);
    const currentColors = getColorsForPhase(currentPhase);

    // Pour une progression linéaire plus fluide, on peut interpoler entre phases adjacentes
    // basé sur la position précise du soleil
    const sunTimes = SunCalc.getTimes(now, userLocation.lat, userLocation.lon);
    const currentHour = now.getHours() + now.getMinutes() / 60 + now.getSeconds() / 3600;

    // Calculer un facteur de progression pour l'interpolation entre phases
    let interpolationFactor = 0;
    let nextPhase = currentPhase;

    // Déterminer la phase suivante et le facteur d'interpolation pour une progression linéaire
    const sunrise = sunTimes.sunrise.getHours() + sunTimes.sunrise.getMinutes() / 60;
    const sunset = sunTimes.sunset.getHours() + sunTimes.sunset.getMinutes() / 60;
    const civilDawn = sunTimes.dawn.getHours() + sunTimes.dawn.getMinutes() / 60;
    const civilDusk = sunTimes.dusk.getHours() + sunTimes.dusk.getMinutes() / 60;

    // Interpolation linéaire entre les phases pour des transitions ultra-fluides
    if (currentPhase === SolarPhase.CIVIL_DAWN && currentHour < sunrise) {
      const progress = (currentHour - civilDawn) / (sunrise - civilDawn);
      interpolationFactor = Math.max(0, Math.min(1, progress));
      nextPhase = SolarPhase.SUNRISE;
    } else if (currentPhase === SolarPhase.SUNRISE && currentHour < sunrise + 2) {
      const progress = (currentHour - sunrise) / 2;
      interpolationFactor = Math.max(0, Math.min(1, progress));
      nextPhase = SolarPhase.MORNING;
    } else if (currentPhase === SolarPhase.CIVIL_DUSK && currentHour > civilDusk) {
      const progress = (currentHour - civilDusk) / (sunset - civilDusk);
      interpolationFactor = Math.max(0, Math.min(1, progress));
      nextPhase = SolarPhase.SUNSET;
    }

    // Si on a une interpolation, mélanger les couleurs
    if (interpolationFactor > 0 && nextPhase !== currentPhase) {
      const nextColors = getColorsForPhase(nextPhase);
      return {
        primary: interpolateColor(currentColors.primary, nextColors.primary, interpolationFactor),
        secondary: interpolateColor(currentColors.secondary, nextColors.secondary, interpolationFactor),
        tertiary: interpolateColor(currentColors.tertiary, nextColors.tertiary, interpolationFactor)
      };
    }

    return currentColors;
  };

  // Fonction pour mettre à jour l'arrière-plan avec optimisations
  const updateBackground = () => {
    if (!backgroundRef.current) return;

    const now = getCurrentTime(); // Utiliser le temps du contexte (réel ou simulé)
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentSecond = now.getSeconds();

    // Calcul précis de l'heure avec minutes et secondes
    const preciseHour = currentHour + currentMinute / 60 + currentSecond / 3600;

    // 🔍 DEBUG CRITIQUE: Vérifier si le temps change bien
    console.log(`🕐 TEMPS ACTUEL: ${now.toLocaleTimeString()} (${preciseHour.toFixed(2)}h) - Simulé: ${getCurrentTime() !== new Date() ? 'OUI' : 'NON'}`);

    // Obtenir les couleurs actuelles basées sur la position solaire réelle
    const colors = getCurrentColors();

    // Créer un dégradé naturel et progressif pour le ciel
    // Direction: du bas (horizon) vers le haut (ciel)
    const gradient = `linear-gradient(to top,
      ${colors.primary} 0%,
      ${colors.secondary} 50%,
      ${colors.tertiary} 100%)`;

    // Calculer la nouvelle luminosité du paysage
    const newBrightness = calculateLandscapeBrightness(now);

    // 🌅 DEBUG: Informations sur les couleurs et la phase solaire
    console.log(`🔍 === MISE À JOUR DÉGRADÉ ===`);
    console.log(`🕐 Heure: ${preciseHour.toFixed(2)}h (${currentHour}:${currentMinute}:${currentSecond})`);
    console.log(`🎨 Couleurs: Primary=${colors.primary}, Secondary=${colors.secondary}, Tertiary=${colors.tertiary}`);
    console.log(`🎨 Dégradé: ${gradient}`);
    console.log(`💡 Luminosité paysage: ${newBrightness.toFixed(2)}`);
    console.log(`📍 Géolocalisation prête: ${locationReady}`);

    if (locationReady) {
      const currentPhase = getCurrentSolarPhase(now);
      const sunTimes = SunCalc.getTimes(now, userLocation.lat, userLocation.lon);
      console.log(`☀️ Phase solaire: ${currentPhase}`);
      console.log(`🌅 Lever: ${sunTimes.sunrise.toLocaleTimeString()}`);
      console.log(`🌇 Coucher: ${sunTimes.sunset.toLocaleTimeString()}`);
    }

    // Diagnostic de l'élément DOM
    if (backgroundRef.current) {
      console.log(`🏗️ Élément DOM trouvé: ${backgroundRef.current.tagName}`);
      console.log(`🎨 Style background AVANT: "${backgroundRef.current.style.background}"`);
      console.log(`🎨 Style backgroundImage AVANT: "${backgroundRef.current.style.backgroundImage}"`);
      console.log(`📏 Classes CSS: "${backgroundRef.current.className}"`);

      // Vérifier les styles calculés
      const computedStyle = window.getComputedStyle(backgroundRef.current);
      console.log(`💻 Style calculé background: "${computedStyle.background}"`);
      console.log(`💻 Style calculé backgroundImage: "${computedStyle.backgroundImage}"`);
    }

    // 🔧 CORRECTION: Logique de mise à jour optimisée pour permettre les micro-transitions
    // Éviter uniquement les mises à jour identiques consécutives, mais permettre les changements subtils
    const updateKey = `${gradient}-${newBrightness.toFixed(3)}`; // Précision à 3 décimales pour micro-transitions
    if (lastUpdateRef.current === updateKey) return;
    lastUpdateRef.current = updateKey;

    // Mettre à jour l'état de la luminosité
    setLandscapeBrightness(newBrightness);

    // Annuler l'animation précédente pour éviter les conflits
    if (timelineRef.current) {
      timelineRef.current.kill();
    }

    // Appliquer le dégradé et la luminosité avec animation GSAP UNIQUEMENT
    if (backgroundRef.current) {
      console.log(`🎨 Application du dégradé: ${gradient}`);

      // Annuler l'animation précédente pour éviter les conflits
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // Créer une nouvelle timeline pour une transition fluide
      timelineRef.current = gsap.timeline();

      // Animation du dégradé de fond
      timelineRef.current.to(backgroundRef.current, {
        background: gradient,
        duration: 2.0,
        ease: "power2.inOut",
        force3D: true,
        willChange: "background"
      });

      // Animation synchronisée de la luminosité du paysage
      if (landscapeRef.current) {
        console.log(`💡 AVANT GSAP - Luminosité actuelle: ${window.getComputedStyle(landscapeRef.current).filter}`);

        timelineRef.current.to(landscapeRef.current, {
          filter: `brightness(${newBrightness})`,
          duration: 2.0, // Même durée que le dégradé pour une synchronisation parfaite
          ease: "power2.inOut", // Même courbe d'easing pour une harmonie parfaite
          onComplete: () => {
            console.log(`💡 APRÈS GSAP - Luminosité appliquée: ${window.getComputedStyle(landscapeRef.current!).filter}`);
          }
        }, 0); // Démarrer en même temps que l'animation du fond

        console.log(`💡 Luminosité paysage animée via GSAP: ${newBrightness} (${(newBrightness * 100).toFixed(0)}%)`);
      }
    }
  };

  // Fonction optimisée pour les mises à jour en temps réel
  const scheduleUpdate = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    animationFrameRef.current = requestAnimationFrame(() => {
      updateBackground();
      // Programmer la prochaine mise à jour
      setTimeout(scheduleUpdate, 1000);
    });
  };

  // Créer l'animation de zoom profond sur le paysage avec retour à la position initiale
  const createLandscapeZoomAnimation = () => {
    if (!landscapeRef.current) return;

    // Nettoyer l'animation précédente
    if (zoomTimelineRef.current) {
      zoomTimelineRef.current.kill();
    }

    // Animation de zoom plus profond et immersif
    zoomTimelineRef.current = gsap.timeline({
      repeat: -1,
      yoyo: false,
      force3D: true,
      willChange: "transform"
    });

    // Phase 1 : Zoom in progressif et plus profond (de 100% à 115% en 45 secondes)
    // Zoom plus profond pour une expérience plus immersive
    zoomTimelineRef.current.to(landscapeRef.current, {
      scale: 1.15, // Zoom plus profond (15% au lieu de 5%)
      duration: 45, // Plus long pour un effet plus contemplatif
      ease: "power2.inOut" // Courbe plus douce pour un effet naturel
    });

    // Phase 2 : Maintien du zoom maximum (pause de 5 secondes)
    // Permet d'apprécier la profondeur avant le retour
    zoomTimelineRef.current.to(landscapeRef.current, {
      scale: 1.15,
      duration: 5,
      ease: "none"
    });

    // Phase 3 : Retour progressif à l'état initial (de 115% à 100% en 35 secondes)
    // Retour plus rapide que l'aller pour créer un rythme naturel
    zoomTimelineRef.current.to(landscapeRef.current, {
      scale: 1.0,
      duration: 35,
      ease: "power2.out" // Décélération douce pour un retour naturel
    });

    // Phase 4 : Pause à l'état initial (10 secondes)
    // Permet de bien percevoir le retour à la position de départ
    zoomTimelineRef.current.to(landscapeRef.current, {
      scale: 1.0,
      duration: 10,
      ease: "none"
    });

    console.log('🔍 Animation de zoom profond du paysage initialisée (cycle de 95 secondes - zoom 15%)');
  };

  // useEffect pour l'initialisation (une seule fois)
  useEffect(() => {
    // 🔍 TEST: Forcer le dégradé avec JavaScript pur
    if (backgroundRef.current) {
      const element = backgroundRef.current;

      // Supprimer toutes les classes qui pourraient interférer
      element.className = '';

      // Appliquer le style avec JavaScript pur et !important
      element.style.cssText = `
        position: relative;
        overflow: hidden;
        min-height: 100vh;
        background: linear-gradient(to top, #ff0000 0%, #00ff00 50%, #0000ff 100%) !important;
        background-image: linear-gradient(to top, #ff0000 0%, #00ff00 50%, #0000ff 100%) !important;
      `;

      console.log('🔍 STYLE FORCÉ AVEC JAVASCRIPT:', element.style.cssText);
      console.log('🔍 STYLE CALCULÉ:', window.getComputedStyle(element).background);
    }

    // Démarrer l'animation de zoom subtil
    createLandscapeZoomAnimation();

    // Démarrer le cycle de mise à jour pour les changements de temps
    scheduleUpdate();

    // Nettoyage complet
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (timelineRef.current) {
        timelineRef.current.kill();
      }
      if (zoomTimelineRef.current) {
        zoomTimelineRef.current.kill();
      }
    };
  }, []); // Une seule fois au montage

  // 🔍 TEST: Forcer l'exécution pour voir l'effet de géolocalisation
  useEffect(() => {
    // Forcer l'exécution même sans géolocalisation pour tester
    if (backgroundRef.current) {
      console.log(`🔍 TEST FORCÉ: Application du dégradé de géolocalisation...`);

      // 🔧 CORRECTION: Dégradé ÉTENDU profitant de l'espace ciel supplémentaire
      const colors = getCurrentColors();
      // Dégradé étendu avec plus d'espace pour les transitions
      // Profiter de l'espace ciel libéré par le décalage de l'image
      const optimizedGradient = `linear-gradient(to bottom,
        ${colors.tertiary} 0%,
        ${colors.secondary} 8%,
        ${colors.primary} 18%,
        ${colors.primary}ee 30%,
        ${colors.primary}cc 45%,
        ${colors.primary}99 60%,
        ${colors.primary}66 75%,
        ${colors.primary}33 85%,
        ${colors.primary}11 92%,
        transparent 100%)`;
      backgroundRef.current.style.setProperty('background', optimizedGradient, 'important');

      console.log('🔍 COULEURS CALCULÉES:', colors);
      console.log('🔧 DÉGRADÉ OPTIMISÉ POUR VISIBILITÉ:', optimizedGradient);
    }

    // Version originale commentée pour comparaison
    /*
    if (locationReady && backgroundRef.current) {
      console.log(`🌍 POSITION CHANGÉE: ${userLocation.lat.toFixed(4)}, ${userLocation.lon.toFixed(4)} - Recalcul du dégradé...`);
      const colors = getCurrentColors();
      const gradient = `linear-gradient(to top, ${colors.primary} 25%, ${colors.secondary} 50%, ${colors.tertiary} 85%)`;
      backgroundRef.current.style.background = gradient;
    }
    */
  }, [userLocation.lat, userLocation.lon, locationReady]);



  // Obtenir les couleurs actuelles pour le fallback
  const currentColors = getCurrentColors();
  const fallbackGradient = `linear-gradient(to top, ${currentColors.primary} 25%, ${currentColors.secondary} 50%, ${currentColors.tertiary} 85%)`;

  return (
    <div
      ref={backgroundRef}
      className="relative overflow-hidden"
      style={{
        minHeight: '100vh',
        background: fallbackGradient,
        backgroundAttachment: 'fixed',
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover'
      }}
    >
      <AstronomicalLayer />
      <DiurnalLayer />

      {/* Image de paysage avec effet de luminosité dynamique et zoom subtil */}
      <div
        ref={landscapeRef}
        className="fixed inset-0 w-full h-full bg-cover bg-center bg-no-repeat pointer-events-none"
        style={{
          backgroundImage: 'url(/Background.png)',
          backgroundPosition: 'center 75%', // 🔧 CORRECTION: Décaler vers le bas pour libérer plus de ciel
          backgroundSize: 'cover',
          zIndex: 5, // Devant les nuages (z-index 2) mais derrière les étoiles (z-index 10)
          // 🔧 CORRECTION CRITIQUE: Supprimer le filter CSS pour éviter conflit avec GSAP
          // filter: `brightness(${landscapeBrightness})`, // SUPPRIMÉ - géré par GSAP uniquement
          transformOrigin: 'center center', // Point d'origine pour le zoom
          willChange: 'transform, filter' // Optimisation GPU
        }}
      />

      {/* Contenu principal avec z-index élevé */}
      <div className="relative" style={{ zIndex: 10 }}>
        {children}
      </div>

      {/* Style pour neutraliser les conflits CSS */}
      <style dangerouslySetInnerHTML={{
        __html: `
          body {
            background: none !important;
            background-color: transparent !important;
          }
          html {
            background: none !important;
            background-color: transparent !important;
          }
        `
      }} />
    </div>
  );
};

export default DynamicBackground;
