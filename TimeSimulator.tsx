import React, { useState } from 'react';
import { useLocation } from '../Context/LocationContext';
import * as SunCalc from 'suncalc';

interface TimeSimulatorProps {
  onTimeChange: (simulatedTime: Date) => void;
  currentSimulatedTime: Date;
}

const TimeSimulator: React.FC<TimeSimulatorProps> = ({ onTimeChange, currentSimulatedTime }) => {
  const { userLocation, locationReady } = useLocation();
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // Obtenir les heures solaires pour aujourd'hui
  const getSunTimes = () => {
    if (!locationReady) return null;
    return SunCalc.getTimes(new Date(), userLocation.lat, userLocation.lon);
  };

  const sunTimes = getSunTimes();

  // Créer une date simulée avec l'heure spécifiée
  const createSimulatedTime = (hours: number, minutes: number = 0) => {
    const now = new Date();
    const simulated = new Date(now);
    simulated.setHours(hours, minutes, 0, 0);
    return simulated;
  };

  // Phases de test prédéfinies
  const testPhases = [
    { name: '🌌 Nuit profonde', time: createSimulatedTime(2, 0) },
    { name: '🌠 Aube astronomique', time: sunTimes ? new Date(sunTimes.nightEnd.getTime() + 15 * 60000) : createSimulatedTime(5, 0) },
    { name: '⚓ Aube nautique', time: sunTimes ? new Date(sunTimes.nauticalDawn.getTime() + 15 * 60000) : createSimulatedTime(5, 30) },
    { name: '🌅 Aube civile', time: sunTimes ? new Date(sunTimes.dawn.getTime() + 15 * 60000) : createSimulatedTime(6, 0) },
    { name: '🌄 Lever du soleil', time: sunTimes ? sunTimes.sunrise : createSimulatedTime(7, 0) },
    { name: '🌤️ Matin', time: sunTimes ? new Date(sunTimes.sunrise.getTime() + 2 * 3600000) : createSimulatedTime(9, 0) },
    { name: '☀️ Midi', time: createSimulatedTime(12, 0) },
    { name: '🌤️ Après-midi', time: createSimulatedTime(15, 0) },
    { name: '🌇 Coucher du soleil', time: sunTimes ? sunTimes.sunset : createSimulatedTime(18, 0) },
    { name: '🌆 Crépuscule civil', time: sunTimes ? new Date(sunTimes.dusk.getTime() + 15 * 60000) : createSimulatedTime(19, 0) },
    { name: '🌃 Crépuscule nautique', time: sunTimes ? new Date(sunTimes.nauticalDusk.getTime() + 15 * 60000) : createSimulatedTime(20, 0) },
    { name: '🌌 Crépuscule astronomique', time: sunTimes ? new Date(sunTimes.night.getTime() + 15 * 60000) : createSimulatedTime(21, 0) },
  ];

  // Contrôle manuel de l'heure
  const handleTimeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const [hours, minutes] = event.target.value.split(':').map(Number);
    const newTime = createSimulatedTime(hours, minutes);
    onTimeChange(newTime);
  };

  // Retour au temps réel
  const resetToRealTime = () => {
    onTimeChange(new Date());
  };

  // Formatage de l'heure pour l'affichage
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('fr-FR', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 bg-purple-600/80 hover:bg-purple-700/80 text-white p-3 rounded-full backdrop-blur-sm transition-all duration-300 z-40 shadow-lg"
        title="Simulateur de temps (DEV uniquement)"
      >
        ⏰
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 left-4 bg-black/90 text-white p-4 rounded-lg backdrop-blur-sm z-40 max-w-sm shadow-xl">
      <div className="flex justify-between items-start mb-3">
        <h3 className="text-lg font-bold text-purple-400">⏰ Simulateur de Temps</h3>
        <button
          onClick={() => setIsVisible(false)}
          className="text-gray-400 hover:text-white text-xl leading-none"
        >
          ×
        </button>
      </div>

      {/* Affichage du temps actuel */}
      <div className="mb-3 p-2 bg-gray-800 rounded">
        <div className="text-xs text-gray-400">Temps simulé:</div>
        <div className="text-lg font-mono text-purple-300">{formatTime(currentSimulatedTime)}</div>
      </div>

      {/* Contrôle manuel */}
      <div className="mb-3">
        <label className="block text-xs text-gray-400 mb-1">Heure manuelle:</label>
        <div className="flex gap-2">
          <input
            type="time"
            value={`${currentSimulatedTime.getHours().toString().padStart(2, '0')}:${currentSimulatedTime.getMinutes().toString().padStart(2, '0')}`}
            onChange={handleTimeChange}
            className="bg-gray-700 text-white px-2 py-1 rounded text-sm flex-1"
          />
          <button
            onClick={resetToRealTime}
            className="bg-green-600 hover:bg-green-700 px-2 py-1 rounded text-xs transition-colors"
            title="Retour au temps réel"
          >
            🔄
          </button>
        </div>
      </div>

      {/* Bouton pour étendre les phases */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="w-full bg-purple-600 hover:bg-purple-700 px-3 py-2 rounded text-sm transition-colors mb-3"
      >
        {isExpanded ? '🔼 Masquer les phases' : '🔽 Phases solaires'}
      </button>

      {/* Phases de test */}
      {isExpanded && (
        <div className="border-t border-gray-600 pt-3">
          <div className="mb-2 text-xs font-semibold">🌅 Phases solaires:</div>
          <div className="grid gap-1 max-h-48 overflow-y-auto">
            {testPhases.map((phase, index) => (
              <button
                key={index}
                onClick={() => onTimeChange(phase.time)}
                className="text-left bg-gray-700 hover:bg-gray-600 px-2 py-1 rounded text-xs transition-colors"
                title={`Simuler ${phase.name} à ${formatTime(phase.time)}`}
              >
                {phase.name}
                <span className="text-gray-400 ml-1">({formatTime(phase.time)})</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Informations sur la géolocalisation */}
      {locationReady && sunTimes && (
        <div className="mt-3 pt-3 border-t border-gray-600 text-xs text-gray-400">
          <div>📍 Position: {userLocation.lat.toFixed(2)}, {userLocation.lon.toFixed(2)}</div>
          <div>🌅 Lever: {sunTimes.sunrise.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}</div>
          <div>🌇 Coucher: {sunTimes.sunset.toLocaleTimeString('fr-FR', { hour: '2-digit', minute: '2-digit' })}</div>
        </div>
      )}
    </div>
  );
};

export default TimeSimulator;
